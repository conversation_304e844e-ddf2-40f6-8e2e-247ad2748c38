export enum EDex {
  // Sui Network DEXes
  CETUS = "cetus",
  BLUEMOVE = "bluemove",
  MOVEPUMP = "movepump",
  LIQUIDS = "liquids",
  ANIME = "anime",
  AUX = "aux",
  PANCAKE = "pancake",
  THALA = "thala",
  CELLANA = "cellana",
  FLOWX = "flowx",
  TURBOSFINANCE = "turbosfinance",
  TURBOSFUN = "turbosfun",
  UPTOS = "uptos",
  SEVENKFUN = "sevenkfun",
  BLUEFIN = "bluefin",
  SUIAIFUN = "suiai",
  MOONBAGS = "moonbags",
  FLOWX_V3 = "flowxv3",
  STEAM = "steamm",
  BLASTFUN = "blastfun",

  // Hypervm Network DEXes
  HYPERSWAPV3 = "hyperswapv3",
  PROJECTX = "projectx",
}

export const NETWORK_DEXES = {
  sui: [
    EDex.CETUS,
    EDex.BLUEMOVE,
    EDex.MOVEPUMP,
    EDex.LIQUIDS,
    EDex.ANIME,
    EDex.AUX,
    EDex.PANCAKE,
    EDex.THALA,
    EDex.CELLANA,
    EDex.FLOWX,
    EDex.TURBOSFINANCE,
    EDex.TURBOSFUN,
    EDex.UPTOS,
    EDex.SEVENKFUN,
    EDex.BLUEFIN,
    EDex.SUIAIFUN,
    EDex.MOONBAGS,
    EDex.FLOWX_V3,
    EDex.STEAM,
    EDex.BLASTFUN,
  ],
  hyperevm: [EDex.HYPERSWAPV3, EDex.PROJECTX],
  somnia: [],
} as const;

export const NETWORK_MEME_DEXES = {
  sui: [EDex.MOONBAGS, EDex.TURBOSFUN, EDex.SUIAIFUN, EDex.BLASTFUN],
  hyperevm: [EDex.PROJECTX],
  somnia: [],
} as const;

export const MEME_DEXES = NETWORK_MEME_DEXES.sui;

export const getDexesForNetwork = (network: string): EDex[] => {
  const networkDexes = NETWORK_DEXES[network as keyof typeof NETWORK_DEXES];
  return [...(networkDexes || NETWORK_DEXES.sui)];
};

export const getMemeDexesForNetwork = (network: string): EDex[] => {
  const networkMemeDexes =
    NETWORK_MEME_DEXES[network as keyof typeof NETWORK_MEME_DEXES];
  return [...(networkMemeDexes || NETWORK_MEME_DEXES.sui)];
};

export const isMemeDexForNetwork = (dex: EDex, network: string): boolean => {
  const memeDexes = getMemeDexesForNetwork(network);
  return memeDexes.includes(dex);
};

export const isDexSupportedOnNetwork = (
  dex: EDex,
  network: string
): boolean => {
  const networkDexes = getDexesForNetwork(network);
  return networkDexes.includes(dex);
};
