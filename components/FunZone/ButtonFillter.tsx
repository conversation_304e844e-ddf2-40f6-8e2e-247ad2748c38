import { default as React, useEffect, useMemo, useState } from "react";
import { Setting } from "@/assets/icons";
import { ModalFilterMeme } from "@/modals";
import { MEME_DEXES, getMemeDexesForNetwork } from "@/enums";
import { useNetwork } from "@/context/network";
import { isEmpty } from "lodash";

export const getDexesDefaultForNetwork = (network: string): string => {
  return getMemeDexesForNetwork(network).join(",");
};

export const DEXES_DEFAULT = MEME_DEXES.join(",");

interface IDataParams {
  dexes: string[];
  advancedFilters: any[];
}

export const ButtonFilter = ({
  params,
  setParams,
  title,
  height = 42,
}: {
  params: any;
  title: string;
  height?: number;
  setParams: (params: any) => void;
}) => {
  const { currentNetwork } = useNetwork();
  const [isShowFilter, setIsShowFilter] = useState<boolean>(false);
  const [dataParams, setDataParams] = useState<IDataParams>({
    dexes: [],
    advancedFilters: [],
  });

  const networkMemeDexes = useMemo(() => {
    return getMemeDexesForNetwork(currentNetwork);
  }, [currentNetwork]);

  useEffect(() => {
    if (!!dataParams.dexes.length) return;
    setDataParams({
      ...dataParams,
      dexes: [...networkMemeDexes],
    });
  }, [networkMemeDexes]);

  useEffect(() => {
    const advancedFilters: Array<any> = [];

    // Convert params to dataParams format
    if (params.dexes) {
      setDataParams((prev) => ({
        ...prev,
        dexes: params.dexes.split(","),
      }));
    }

    // Handle advanced filters
    const filterFields = [
      { name: "Bonding curve", value: "bondingCurve" },
      { name: "Dev holding", value: "devHolding" },
      { name: "Holders", value: "holders" },
      { name: "Liquidity", value: "liquidity" },
      { name: "Volume", value: "volume" },
      { name: "Market Cap", value: "marketCap" },
      { name: "Txns", value: "txns" },
      { name: "Buys", value: "buys" },
      { name: "Sells", value: "sells" },
      { name: "Token Age", value: "tokenAge" },
    ];

    filterFields.forEach((field) => {
      if (params[field.value]) {
        const [from, to] = params[field.value].split("-");
        advancedFilters.push({
          name: field.name,
          value: field.value,
          from,
          to,
        });
      }
    });

    setDataParams((prev) => ({
      ...prev,
      advancedFilters,
    }));
  }, [params]);
  const isFiltering = useMemo(() => {
    if (isEmpty(params)) {
      return false;
    }

    if (dataParams?.advancedFilters?.length > 0) {
      return true;
    }

    const isDefaultDex = dataParams?.dexes?.length == networkMemeDexes.length;
    return !isDefaultDex;
  }, [JSON.stringify(params), params.dexes, dataParams]);

  return (
    <div className="tablet:w-[70px] flex items-center">
      <div
        style={{ height: `${height}px` }}
        onClick={() => setIsShowFilter(true)}
        className="tablet:h-[42px] bg-white-100 hover:bg-white-50 body-sm-medium-12 flex aspect-square h-[32px] cursor-pointer items-center justify-center gap-[4px] rounded-[6px] px-[8px] md:h-[36px]"
      >
        <div className="relative">
          <Setting />
          {isFiltering && (
            <div className="bg-brand-500 absolute right-[-2px] top-[-2px] h-[6px] w-[6px] rounded-full" />
          )}
        </div>
        <div className="hidden md:block">Filters</div>
      </div>

      <ModalFilterMeme
        title={title}
        dataParams={dataParams}
        setDataParams={setDataParams}
        setParams={setParams}
        isOpen={isShowFilter}
        onClose={() => setIsShowFilter(false)}
      />
    </div>
  );
};
