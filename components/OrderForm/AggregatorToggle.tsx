import React from "react";
import { AppToggle } from "@/components";
import { TPair } from "@/types";
import { EDex, MEME_DEXES, isMemeDexForNetwork } from "@/enums";
import { useNetwork } from "@/context/network";

interface AggregatorToggleProps {
  useAggregator: boolean;
  onToggle: (value: boolean) => void;
  disabled?: boolean;
  pair?: TPair;
}

export const AggregatorToggle: React.FC<AggregatorToggleProps> = ({
  useAggregator,
  onToggle,
  disabled = false,
  pair,
}) => {
  const { currentNetwork } = useNetwork();
  const shouldHide =
    pair?.dex?.dex && isMemeDexForNetwork(pair.dex.dex as EDex, currentNetwork);

  if (shouldHide) {
    return null;
  }

  return (
    <div className="text-neutral-alpha-500 body-sm-medium-12 flex items-center gap-1">
      <AppToggle
        value={useAggregator}
        onChange={() => onToggle(!useAggregator)}
        disabled={disabled}
      />
      <div className="flex items-center gap-1">Aggregator</div>
    </div>
  );
};
