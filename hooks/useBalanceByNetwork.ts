import { hyperPublicProvider } from "@/app/providers/networkChains";
import { useNetwork } from "@/context";
import { NETWORKS } from "@/utils/contants";
import { convertMistToDec } from "@/utils/helper";
import { getSuiBalanceOnchain } from "@/utils/suiClient";
import { useEffect, useState } from "react";
import { formatEther } from "viem";

export const useBalanceByNetwork = (address: string) => {
  const { currentNetwork } = useNetwork();
  const [nativeBalance, setNativeBalance] = useState<string | number>();

  const getSuiBalance = async () => {
    try {
      const balance = await getSuiBalanceOnchain(address);
      setNativeBalance(convertMistToDec(balance));
    } catch (error) {
      console.error(`getSuiBalance error: ${error}`);
    }
  };

  const getHypeBalance = async () => {
    try {
      const balance = await hyperPublicProvider.getBalance({
        address: address as any,
      });

      setNativeBalance(formatEther(balance));
    } catch (error) {
      console.error("getHypeBalance error:", error);
    }
  };

  const getSomniaBalance = async () => {
    try {
      const balance = await hyperPublicProvider.getBalance({
        address: address as any,
      });

      setNativeBalance(formatEther(balance));
    } catch (error) {
      console.error("getSomniaBalance error:", error);
    }
  };

  useEffect(() => {
    if (!address) return;

    switch (currentNetwork) {
      case NETWORKS.SUI:
        getSuiBalance();
        break;
      case NETWORKS.HYPEREVM:
        getHypeBalance();
        break;
      case NETWORKS.SOMNIA:
        getSomniaBalance();
        break;
      default:
        getSuiBalance();
    }
  }, [address, currentNetwork]);

  return {
    nativeBalance,
  };
};
