import { createPublicClient, http } from "viem";

export const hypeMainnet<PERSON>hain = {
  id: 999,
  name: "Hype",
  network: "hype",
  nativeCurrency: {
    name: "Hype",
    symbol: "HYPE",
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ["https://rpc.hyperliquid.xyz/evm"],
    },
    public: {
      http: ["https://rpc.hyperliquid.xyz/evm"],
    },
  },
  blockExplorers: {
    default: {
      name: "Hype Explorer",
      url: "https://explorer.hyperliquid.xyz",
    },
  },
};

export const somniaMainnetChain = {
  id: 2648,
  name: "Somnia",
  network: "somnia",
  nativeCurrency: {
    name: "Somnia",
    symbol: "SOMI",
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ["https://rpc.somnia.network"],
    },
    public: {
      http: ["https://rpc.somnia.network"],
    },
  },
  blockExplorers: {
    default: {
      name: "Somnia Explorer",
      url: "https://explorer.somnia.network",
    },
  },
};

// Network-specific configurations
export const NETWORK_CONFIGS = {
  sui: {
    name: "Sui",
    symbol: "SUI",
    decimals: 9,
    chainType: "sui",
    nativeTokenAddress: {
      full: "******************************************000000000000000000000002::sui::SUI",
      short: "0x2::sui::SUI",
    },
  },
  hyperevm: {
    name: "Hype",
    symbol: "HYPE",
    decimals: 18,
    chainType: "ethereum",
    chain: hypeMainnetChain,
    nativeTokenAddress: {
      full: "******************************************",
      short: "******************************************",
    },
  },
  somnia: {
    name: "Somnia",
    symbol: "SOMI",
    decimals: 18,
    chainType: "ethereum",
    chain: somniaMainnetChain,
    nativeTokenAddress: {
      full: "******************************************",
      short: "******************************************",
    },
  },
} as const;

export type NetworkType = keyof typeof NETWORK_CONFIGS;

export const getNetworkConfig = (network: string) => {
  return NETWORK_CONFIGS[network as NetworkType];
};

export const getNetworkDecimals = (network: string): number => {
  const config = getNetworkConfig(network);
  return config?.decimals || 18;
};

export const getNetworkSymbol = (network: string): string => {
  const config = getNetworkConfig(network);
  return config?.symbol || "UNKNOWN";
};

export const getNetworkNativeTokenAddress = (network: string) => {
  const config = getNetworkConfig(network);
  return config?.nativeTokenAddress;
};

export const hyperPublicProvider = createPublicClient({
  chain: hypeMainnetChain,
  transport: http(),
});
